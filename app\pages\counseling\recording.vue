<template>
  <view class="recording-page">
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <view class="header">
      <view class="back-button" @click="confirmGoBack">
        <image class="icon" src="/static/icons/back.svg" mode="aspectFit"></image>
      </view>
      <text class="title">录音</text>
      <view class="placeholder"></view>
    </view>

    <view class="content">
      <view class="visualization-container">
        <view class="visualization-circle" :style="{ transform: `scale(${1 + (audioLevel / 100) * 0.5})` }">
          <view class="inner-circle" :style="{ opacity: audioLevel / 100 }"></view>
        </view>
      </view>

      <view class="recording-info">
        <text class="recording-time">{{ formatTime(recordingTime) }}</text>
        <text class="recording-status">{{ isRecording ? '正在录音...' : '准备录音' }}</text>
        <text v-if="isRecording" class="recording-hint">录音已自动开始，请开始您的咨询对话</text>
      </view>

      <view class="controls">
        <view v-if="!isRecording" class="control-button start-button" @click="startRecording">
          <text class="button-text">开始录音</text>
        </view>
        <view v-else class="control-button stop-button" @click="stopRecording">
          <text class="button-text">停止录音并分析</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { APP_SETTINGS } from '../../config/appConfig';
import counselingService from '../../services/counselingService';

export default {
  data() {
    return {
      statusBarHeight: 20,
      isRecording: false,
      recordingTime: 0,
      audioLevel: 0,
      audioUri: null,
      sessionId: null,
      timerRef: null,
      audioLevelTimerRef: null,
      recorderManager: null,
      isUploading: false,
      recordingId: null,
      userCancelled: false, // 用户取消标志
      recordingStartTime: null, // 录音开始时间戳
      timerStorageKey: null // 计时器存储键
    }
  },
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;

    // 获取会话ID
    if (options.sessionId) {
      this.sessionId = options.sessionId;
      this.timerStorageKey = `recording_timer_${this.sessionId}`;

      // 检查是否有正在进行的录音计时
      this.restoreRecordingState();

      // 页面加载完成后自动开始录音
      // 使用setTimeout确保页面完全加载
      setTimeout(() => {
        if (!this.isRecording) {
          this.startRecording();
        }
      }, 500);
    } else {
      uni.showToast({
        title: '缺少会话ID',
        icon: 'none'
      });
      setTimeout(() => {
        this.goBack();
      }, 1500);
    }
  },
  onUnload() {
    // 页面卸载时清理资源
    this.cleanupResources();
  },
  onShow() {
    // 页面显示时恢复录音状态
    if (this.sessionId && this.timerStorageKey) {
      this.restoreRecordingState();
    }
  },
  onHide() {
    // 页面隐藏时保存录音状态
    if (this.isRecording) {
      this.saveRecordingState();
    }
  },
  // 监听返回按钮事件
  onBackPress() {
    // 如果正在录音，拦截返回事件并显示确认对话框
    if (this.isRecording) {
      this.confirmGoBack();
      return true; // 返回 true 表示拦截默认返回行为
    }
    // 不拦截返回事件
    return false;
  },
  methods: {
    // 格式化时间
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 恢复录音状态
    restoreRecordingState() {
      try {
        const savedState = uni.getStorageSync(this.timerStorageKey);
        if (savedState && savedState.isRecording && savedState.startTime) {
          console.log('发现正在进行的录音，恢复状态:', savedState);

          // 计算已经录音的时间
          const currentTime = Date.now();
          const elapsedSeconds = Math.floor((currentTime - savedState.startTime) / 1000);

          // 恢复录音状态
          this.isRecording = true;
          this.recordingTime = elapsedSeconds;
          this.recordingStartTime = savedState.startTime;

          // 重新开始计时器
          this.startTimer();

          // 重新开始音量监测
          this.startAudioLevelMonitoring();

          console.log(`录音状态已恢复，已录音时间: ${elapsedSeconds}秒`);
        }
      } catch (error) {
        console.error('恢复录音状态失败:', error);
      }
    },

    // 保存录音状态
    saveRecordingState() {
      try {
        if (this.isRecording && this.recordingStartTime) {
          const state = {
            isRecording: true,
            startTime: this.recordingStartTime,
            sessionId: this.sessionId
          };
          uni.setStorageSync(this.timerStorageKey, state);
        }
      } catch (error) {
        console.error('保存录音状态失败:', error);
      }
    },

    // 清除录音状态
    clearRecordingState() {
      try {
        uni.removeStorageSync(this.timerStorageKey);
      } catch (error) {
        console.error('清除录音状态失败:', error);
      }
    },

    // 开始计时器
    startTimer() {
      // 清除现有计时器
      if (this.timerRef) {
        clearInterval(this.timerRef);
      }

      // 开始新的计时器
      this.timerRef = setInterval(() => {
        if (this.recordingStartTime) {
          // 基于开始时间计算当前录音时长，避免累积误差
          const currentTime = Date.now();
          this.recordingTime = Math.floor((currentTime - this.recordingStartTime) / 1000);
        } else {
          // 如果没有开始时间，使用递增方式（兼容旧逻辑）
          this.recordingTime += 1;
        }
      }, 1000);
    },

    // 开始录音
    async startRecording() {
      try {
        // 创建录音管理器
        this.recorderManager = uni.getRecorderManager();

        // 监听录音事件
        this.recorderManager.onStart(() => {
          console.log('录音已开始');
          this.isRecording = true;

          // 记录录音开始时间
          this.recordingStartTime = Date.now();
          this.recordingTime = 0;

          // 开始计时
          this.startTimer();

          // 保存录音状态
          this.saveRecordingState();

          // 开始监测音量
          this.startAudioLevelMonitoring();
        });

        this.recorderManager.onError((res) => {
          console.error('录音错误:', res);
          uni.showModal({
            title: '录音错误',
            content: res.errMsg || '录音过程中发生错误',
            showCancel: false
          });
        });

        this.recorderManager.onStop((res) => {
          console.log('录音已停止', res);
          this.audioUri = res.tempFilePath;
          this.isRecording = false;

          // 清除计时器
          if (this.timerRef) {
            clearInterval(this.timerRef);
            this.timerRef = null;
          }

          if (this.audioLevelTimerRef) {
            clearInterval(this.audioLevelTimerRef);
            this.audioLevelTimerRef = null;
          }

          // 清除录音状态
          this.clearRecordingState();

          // 检查用户是否已取消，如果取消则不上传录音
          if (!this.userCancelled) {
            // 自动上传录音
            this.uploadRecording();
          } else {
            console.log('用户已取消录音，不上传录音文件');
            uni.hideLoading();
          }
        });

        // 开始录音
        const options = {
          duration: APP_SETTINGS.recording.maxDuration * 1000, // 最长录音时长，单位毫秒
          sampleRate: APP_SETTINGS.recording.sampleRate,
          numberOfChannels: APP_SETTINGS.recording.numberOfChannels,
          encodeBitRate: APP_SETTINGS.recording.bitRate,
          format: 'wav',
          frameSize: 50
        };

        // 直接开始录音，不使用 authorize
        // 在大多数平台上，首次调用录音API时会自动请求权限
        this.recorderManager.start(options);
      } catch (error) {
        console.error('开始录音失败:', error);
        uni.showModal({
          title: '录音失败',
          content: '无法开始录音，请确保已授予应用麦克风权限。',
          showCancel: false
        });
      }
    },

    // 开始监测音量
    startAudioLevelMonitoring() {
      // 使用模拟的方式生成音量数据
      this.audioLevelTimerRef = setInterval(() => {
        if (this.isRecording) {
          // 生成随机音量级别，模拟真实录音环境
          // 这里使用更复杂的算法，使音量变化更自然
          const prevLevel = this.audioLevel;
          const change = Math.random() * 20 - 10; // -10 到 10 的随机变化
          let newLevel = prevLevel + change;

          // 确保音量在 0-100 范围内
          newLevel = Math.max(0, Math.min(100, newLevel));

          // 有时候会有突然的音量峰值
          if (Math.random() < 0.05) { // 5% 的概率
            newLevel = Math.min(100, newLevel + Math.random() * 40);
          }

          this.audioLevel = newLevel;
        }
      }, 100);
    },

    // 停止录音
    stopRecording() {
      if (!this.isRecording) return;

      try {
        // 显示加载提示
        uni.showLoading({
          title: '处理中...'
        });

        // 停止录音
        if (this.recorderManager) {
          this.recorderManager.stop();
        }
      } catch (error) {
        console.error('停止录音失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '停止录音失败',
          icon: 'none'
        });
      }
    },

    // 上传录音
    async uploadRecording() {
      if (!this.audioUri) return;

      // 再次检查用户是否已取消
      if (this.userCancelled) {
        console.log('用户已取消录音，停止上传流程');
        return;
      }

      try {
        this.isUploading = true;

        // 第一步：删除旧的录音和对话记录
        uni.showLoading({
          title: '清理旧数据...'
        });

        try {
          console.log('开始删除旧的录音和对话记录');
          const deleteResult = await counselingService.deleteSessionRecordings(this.sessionId);
          if (deleteResult.success) {
            console.log('旧数据删除成功');
          } else {
            console.warn('删除旧数据失败，但继续上传新录音:', deleteResult.error);
          }
        } catch (deleteError) {
          console.warn('删除旧数据时出错，但继续上传新录音:', deleteError);
        }

        // 第二步：上传新录音
        uni.showLoading({
          title: '上传录音...'
        });

        // 传递录音时长（秒）
        const result = await counselingService.uploadRecording(this.audioUri, this.sessionId, this.recordingTime);

        uni.hideLoading();

        if (result.success) {
          this.recordingId = result.recording_id;

          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });

          // 开始处理录音
          this.processRecording();
        } else {
          uni.showModal({
            title: '上传失败',
            content: result.error || '上传录音失败，请稍后重试',
            showCancel: false
          });
          this.isUploading = false;
        }
      } catch (error) {
        uni.hideLoading();
        console.error('上传录音失败:', error);
        uni.showModal({
          title: '上传失败',
          content: '上传录音失败，请稍后重试',
          showCancel: false
        });
        this.isUploading = false;
      }
    },

    // 处理录音（转录和分析）
    async processRecording() {
      if (!this.recordingId) return;

      // 再次检查用户是否已取消
      if (this.userCancelled) {
        console.log('用户已取消录音，停止处理流程');
        return;
      }

      try {
        uni.showLoading({
          title: '处理中...'
        });

        // 处理录音
        const result = await counselingService.processRecording(this.recordingId);

        if (result.success) {
          // 开始分析
          this.startAnalysis();
        } else {
          uni.hideLoading();
          uni.showModal({
            title: '处理失败',
            content: result.error || '处理录音失败，请稍后重试',
            showCancel: false
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('处理录音失败:', error);
        uni.showModal({
          title: '处理失败',
          content: '处理录音失败，请稍后重试',
          showCancel: false
        });
      }
    },

    // 开始分析
    async startAnalysis() {
      // 再次检查用户是否已取消
      if (this.userCancelled) {
        console.log('用户已取消录音，停止分析流程');
        return;
      }

      try {
        uni.showLoading({
          title: '等待转录完成...'
        });

        // 轮询检查转录是否完成
        let transcriptionComplete = false;
        let attempts = 0;
        const maxAttempts = 15; // 最多等待15次，每次2秒，总共30秒

        while (!transcriptionComplete && attempts < maxAttempts && !this.userCancelled) {
          console.log(`检查转录状态，第${attempts + 1}次尝试`);
          try {
            const transcriptionResult = await counselingService.getTranscription(this.recordingId);
            console.log('转录状态检查结果:', transcriptionResult);

            if (transcriptionResult.success && transcriptionResult.status === 'transcribed') {
              transcriptionComplete = true;
              console.log('转录已完成，可以开始分析');
            } else {
              attempts++;
              // 显示等待进度
              uni.showLoading({
                title: `等待转录完成(${attempts}/${maxAttempts})...`
              });
              // 等待2秒再次检查
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
          } catch (error) {
            console.error('检查转录状态失败:', error);
            attempts++;
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }

        // 检查用户是否在等待过程中取消了
        if (this.userCancelled) {
          console.log('用户在等待转录过程中取消了录音');
          uni.hideLoading();
          return;
        }

        if (!transcriptionComplete) {
          uni.hideLoading();
          uni.showModal({
            title: '转录未完成',
            content: '转录尚未完成，请稍后再试。您可以返回会话详情页，系统会在后台继续处理。',
            showCancel: false,
            success: () => {
              this.goBack(true);
            }
          });
          return;
        }

        // 转录完成后，再次检查用户是否已取消
        if (this.userCancelled) {
          console.log('用户在转录完成后取消了录音');
          uni.hideLoading();
          return;
        }

        // 转录完成后，更新加载提示
        uni.showLoading({
          title: '开始分析...'
        });

        // 开始分析，不重新处理音频
        const result = await counselingService.analyzeDialogue(this.sessionId, {}, false, false);

        uni.hideLoading();

        // 最后一次检查用户是否已取消
        if (this.userCancelled) {
          console.log('用户在分析启动后取消了录音');
          return;
        }

        if (result.success) {
          uni.showToast({
            title: '分析已开始',
            icon: 'success'
          });

          // 设置分析状态为处理中，确保返回后能开始轮询
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];

          if (prevPage && prevPage.$vm) {
            // 直接设置前一页的会话分析状态
            if (prevPage.$vm.session && !prevPage.$vm.session.analysis) {
              prevPage.$vm.session.analysis = {};
            }

            if (prevPage.$vm.session && prevPage.$vm.session.analysis) {
              prevPage.$vm.session.analysis.status = 'processing';

              // 直接调用前一页的开始轮询方法
              if (prevPage.$vm.startPolling) {
                console.log('直接调用前一页的开始轮询方法');
                prevPage.$vm.startPolling();
              }
            }
          }

          console.log('Setting analysis_initiation_flag for session:', this.sessionId);
          uni.setStorageSync('analysis_initiation_flag', {
            sessionId: this.sessionId,
            analysisJustStarted: true,
            timestamp: Date.now()
          });

          // 返回会话详情页
          setTimeout(() => {
            this.goBack(true);
          }, 1500);
        } else {
          uni.showModal({
            title: '分析失败',
            content: result.error || '开始分析失败，请稍后重试',
            showCancel: false
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('开始分析失败:', error);
        uni.showModal({
          title: '分析失败',
          content: '开始分析失败，请稍后重试',
          showCancel: false
        });
      }
    },

    // 清理资源
    cleanupResources() {
      // 清除计时器
      if (this.timerRef) {
        clearInterval(this.timerRef);
        this.timerRef = null;
      }

      if (this.audioLevelTimerRef) {
        clearInterval(this.audioLevelTimerRef);
        this.audioLevelTimerRef = null;
      }

      // 清除录音状态
      this.clearRecordingState();

      // 如果正在录音，停止录音
      if (this.isRecording && this.recorderManager) {
        try {
          this.recorderManager.stop();
        } catch (error) {
          console.error('停止录音失败:', error);
        }
      }
    },

    // 确认返回
    confirmGoBack() {
      // 如果正在录音，显示确认对话框
      if (this.isRecording) {
        uni.showModal({
          title: '确认返回',
          content: '录音尚未完成，返回将停止录音且不会进行分析。确定要返回吗？',
          confirmText: '确定返回',
          cancelText: '继续录音',
          success: (res) => {
            if (res.confirm) {
              // 用户确认返回，设置取消标志
              this.userCancelled = true;
              console.log('用户确认取消录音，设置userCancelled=true');

              // 停止录音但不上传
              this.cleanupResources();

              // 返回上一页，不发出任何事件
              uni.navigateBack();
            }
            // 用户取消，继续录音，不做任何操作
          }
        });
      } else {
        // 如果没有在录音，检查是否正在处理中
        if (this.isUploading) {
          // 如果正在上传或处理，也需要确认
          uni.showModal({
            title: '确认返回',
            content: '录音正在处理中，返回将取消分析。确定要返回吗？',
            confirmText: '确定返回',
            cancelText: '等待完成',
            success: (res) => {
              if (res.confirm) {
                // 用户确认返回，设置取消标志
                this.userCancelled = true;
                console.log('用户在处理过程中确认取消，设置userCancelled=true');

                // 返回上一页，不发出任何事件
                uni.navigateBack();
              }
            }
          });
        } else {
          // 如果没有在录音也没有在处理，直接返回
          uni.navigateBack();
        }
      }
    },

    // 返回上一页
    goBack(refresh = false) {
      // 如果正在录音，先确认
      if (this.isRecording) {
        this.confirmGoBack();
        return;
      }

      if (refresh) {
        // 返回上一页并刷新
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];

        if (prevPage && prevPage.$vm) {
          // 加载会话详情
          if (prevPage.$vm.loadSessionDetail) {
            prevPage.$vm.loadSessionDetail();
          }

          // 加载会话录音记录
          if (prevPage.$vm.loadSessionRecordings) {
            prevPage.$vm.loadSessionRecordings();
          }
        }
      }

      uni.navigateBack();
    }
  }
}
</script>

<style>
.recording-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-primary);
}

.status-bar {
  background-color: var(--primary-color);
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--primary-color);
  color: white;
}

.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 20px;
  height: 20px;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.placeholder {
  width: 32px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.visualization-container {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
}

.visualization-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.1s ease-out;
  box-shadow: 0 0 20px rgba(74, 111, 165, 0.3);
}

.inner-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background-color: white;
  transition: opacity 0.1s ease-out;
}

.recording-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48px;
}

.recording-time {
  font-size: 48px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.recording-status {
  font-size: 16px;
  color: var(--text-secondary);
}

.recording-hint {
  font-size: 14px;
  color: #f5222d;
  margin-top: 12px;
  text-align: center;
  background-color: #fff1f0;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #ffccc7;
  max-width: 80%;
}

.controls {
  width: 100%;
  display: flex;
  justify-content: center;
}

.control-button {
  width: 200px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.start-button {
  background-color: var(--primary-color);
  color: white;
}

.stop-button {
  background-color: #f5222d;
  color: white;
}

.button-text {
  font-size: 18px;
}
</style>
