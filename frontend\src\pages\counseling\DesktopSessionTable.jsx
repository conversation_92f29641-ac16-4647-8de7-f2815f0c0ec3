import React from 'react';
import { Button, Tag, Typography, Popconfirm } from 'antd';
import { ReloadOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import counselingService from '../../services/counselingService';
import { formatDateToBeiJingTime } from '../../utils/dateUtils';

const { Text } = Typography;

const DesktopSessionTable = ({ session, sessionId, loadSession, Modal, message, onDeleteSession }) => {
  const navigate = useNavigate();

  return (
    <div className="desktop-info-card">
      <table className="desktop-table" style={{ width: '100%', tableLayout: 'fixed', borderCollapse: 'collapse', border: '1px solid #f0f0f0' }}>
        <thead>
          <tr style={{ backgroundColor: '#fafafa' }}>
            <th style={{ width: '7%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>会话ID</th>
            <th style={{ width: '9%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>客户姓名</th>
            <th style={{ width: '7%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>客户性别</th>
            <th style={{ width: '7%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>客户年龄</th>
            <th style={{ width: '11%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>会话日期</th>
            <th style={{ width: '9%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>会话时长</th>
            <th style={{ width: '9%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>风险等级</th>
            <th style={{ width: '10%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>操作</th>
            <th style={{ width: '10%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>重新分析</th>
            <th style={{ width: '8%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0', borderRight: '1px solid #f0f0f0' }}>删除</th>
            <th style={{ width: '13%', padding: '12px 8px', textAlign: 'center', borderBottom: '1px solid #f0f0f0' }}>描述</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>{session.id}</td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>{session.client_name || '未指定'}</td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>{{male: '男', female: '女', other: '其他'}[session.client_gender] || session.client_gender || '未指定'}</td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>{session.client_age || '未指定'}</td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>{formatDateToBeiJingTime(session.session_date)}</td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>
              {(() => {
                // 检查是否有录音记录
                const hasRecordings = session.recordings && Array.isArray(session.recordings) && session.recordings.length > 0;

                // 如果有录音记录
                if (hasRecordings) {
                  const recording = session.recordings[0];

                  // 如果录音有时长信息，显示实际录音时长
                  if (recording.duration_seconds && recording.duration_seconds > 0) {
                    const totalSeconds = recording.duration_seconds;
                    const minutes = Math.floor(totalSeconds / 60);
                    const seconds = Math.floor(totalSeconds % 60);
                    return (
                      <>
                        {minutes}:{seconds.toString().padStart(2, '0')}
                        <Text type="secondary" style={{ marginLeft: 8 }}>
                          (实际时长)
                        </Text>
                      </>
                    );
                  }

                  // 如果有录音但没有时长信息，检查是否有分析报告
                  if (session.analysis && session.analysis.status === 'completed') {
                    // 有分析报告但没有时长，显示"已分析"
                    return session.duration_minutes ? `${session.duration_minutes} 分钟 (已分析)` : '已分析';
                  }

                  // 有录音但还没有分析完成，显示配置时长
                  return session.duration_minutes ? `${session.duration_minutes} 分钟 (录音中)` : '录音中';
                }

                // 没有录音记录，显示配置时长或未设置
                return session.duration_minutes ? `${session.duration_minutes} 分钟` : '未设置';
              })()}
            </td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>
              {!session.analysis ? (
                <Tag color="default">未分析</Tag>
              ) : !session.analysis.status || session.analysis.status.toLowerCase() !== 'completed' ? (
                <Tag color="default">{
                  {
                    'pending': '待处理',
                    'processing': '分析中',
                    'failed': '分析失败'
                  }[session.analysis.status ? session.analysis.status.toLowerCase() : 'pending'] || '未知'
                }</Tag>
              ) : !session.analysis.report_content ? (
                <Tag color="default">未知</Tag>
              ) : (
                (() => {
                  // 尝试从分析结果中获取风险等级
                  let riskLevel = '';

                  console.log('桌面端分析数据:', session.analysis);

                  // 从分析结果中获取风险等级 - 修改优先级顺序
                  if (session.analysis.risk_level) {
                    // 直接使用分析结果中的风险等级 - 这应该是最准确的来源
                    riskLevel = session.analysis.risk_level.toLowerCase();
                    console.log('桌面端从risk_level获取风险等级:', riskLevel);
                  } else if (session.analysis.analysis_results && session.analysis.analysis_results.mental_state &&
                            session.analysis.analysis_results.mental_state.risk_assessment &&
                            session.analysis.analysis_results.mental_state.risk_assessment.overall_risk) {
                    // 从结构化分析中获取风险等级
                    riskLevel = session.analysis.analysis_results.mental_state.risk_assessment.overall_risk.toLowerCase();
                    console.log('桌面端从analysis_results.mental_state.risk_assessment.overall_risk获取风险等级:', riskLevel);
                  } else if (session.analysis.analysis_results && session.analysis.analysis_results.risk_assessment &&
                            session.analysis.analysis_results.risk_assessment.level) {
                    // 尝试从另一个可能的路径获取
                    riskLevel = session.analysis.analysis_results.risk_assessment.level.toLowerCase();
                    console.log('桌面端从analysis_results.risk_assessment.level获取风险等级:', riskLevel);
                  } else if (session.analysis.report_content) {
                    // 尝试从报告内容中提取风险等级 - 这是最不可靠的方法，放在最后
                    const reportContent = session.analysis.report_content.toLowerCase();
                    if (reportContent.includes('中等风险') || reportContent.includes('medium risk')) {
                      riskLevel = 'medium';
                    } else if (reportContent.includes('高风险') || reportContent.includes('high risk')) {
                      riskLevel = 'high';
                    } else if (reportContent.includes('危急风险') || reportContent.includes('critical risk')) {
                      riskLevel = 'critical';
                    } else if (reportContent.includes('低风险') || reportContent.includes('low risk')) {
                      riskLevel = 'low';
                    } else {
                      riskLevel = 'low'; // 默认为低风险
                    }
                    console.log('桌面端从报告内容提取风险等级:', riskLevel);
                  } else {
                    // 如果没有找到风险等级，默认为低风险
                    riskLevel = 'low';
                    console.log('桌面端未找到风险等级，默认为低风险');
                  }

                  // 风险等级标签颜色映射
                  const riskLevelColors = {
                    'low': 'success',
                    'medium': 'warning',
                    'high': 'orange',
                    'critical': 'error'
                  };

                  // 风险等级中文名称
                  const riskLevelNames = {
                    'low': '低风险',
                    'medium': '中等风险',
                    'high': '高风险',
                    'critical': '危急风险'
                  };

                  // 如果没有找到风险等级，返回未知
                  if (!riskLevel || !riskLevelNames[riskLevel]) {
                    return <Tag color="default">未知</Tag>;
                  }

                  return <Tag color={riskLevelColors[riskLevel] || 'default'}>{riskLevelNames[riskLevel]}</Tag>;
                })()
              )}
            </td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>
              <div style={{ display: 'flex', justifyContent: 'center', gap: '8px' }}>
                {session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'completed' && session.analysis.report_content ? (
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => navigate(`/counseling/sessions/${session.id}/report`)}
                    style={{ width: '100%' }}
                  >
                    查看报告
                  </Button>
                ) : (session.recordings && Array.isArray(session.recordings) && session.recordings.length > 0) ? (
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      // 开始分析
                      counselingService.analyzeDialogue(parseInt(sessionId), {}, true).then(result => {
                        if (result.success) {
                          message.success('分析已开始，请稍后查看结果');
                          // 重新加载会话数据
                          loadSession();
                        } else {
                          message.error('开始分析失败: ' + (result.error || '未知错误'));
                        }
                      });
                    }}
                    disabled={session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'processing'}
                    loading={session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'processing'}
                    style={{ width: '100%' }}
                  >
                    {session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'processing' ?
                      '分析中...' :
                      (session.analysis && session.analysis.status ? '重新分析' : '开始分析')}
                  </Button>
                ) : (
                  <Button type="default" size="small" disabled style={{ width: '100%' }}>暂无报告</Button>
                )}
              </div>
            </td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>
              {/* 添加调试信息 */}
              {console.log('桌面端录音检查:', {
                'has_recordings_field': !!session.recordings,
                'is_array': Array.isArray(session.recordings),
                'length': session.recordings ? session.recordings.length : 0,
                'first_recording': session.recordings && session.recordings.length > 0 ? session.recordings[0] : null
              })}

              {(session.recordings && Array.isArray(session.recordings) && session.recordings.length > 0) ? (
                <Button
                  type="primary"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    // 显示确认对话框
                    Modal.confirm({
                      title: '重新分析确认',
                      content: '重新分析将覆盖之前的报告结果。如果您需要保存当前报告，请先前往报告页面保存。确定要继续吗？',
                      okText: '继续分析',
                      cancelText: '取消',
                      onOk() {
                        // 重新分析，并重新处理录音文件
                        counselingService.analyzeDialogue(parseInt(sessionId), {}, true, true).then(result => {
                          if (result.success) {
                            message.success('分析已开始，请稍后查看结果');
                            // 重新加载会话数据
                            loadSession();
                          } else {
                            message.error('开始分析失败: ' + (result.error || '未知错误'));
                          }
                        });
                      }
                    });
                  }}
                  disabled={session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'processing'}
                  loading={session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'processing'}
                  style={{ width: '100%' }}
                >
                  {session.analysis && session.analysis.status && session.analysis.status.toLowerCase() === 'processing' ?
                    '分析中...' : '重新分析'}
                </Button>
              ) : (
                <Button type="default" size="small" disabled style={{ width: '100%' }}>暂无录音</Button>
              )}
            </td>
            <td style={{ padding: '12px 8px', textAlign: 'center', borderRight: '1px solid #f0f0f0', verticalAlign: 'middle' }}>
              <Popconfirm
                title="确定要删除这个会话吗？"
                description="删除后将无法恢复，包括所有录音和分析结果。"
                onConfirm={() => onDeleteSession()}
                okText="确定删除"
                cancelText="取消"
              >
                <Button
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                  style={{ width: '100%' }}
                >
                  删除
                </Button>
              </Popconfirm>
            </td>
            <td style={{ padding: '12px 8px', textAlign: 'center', verticalAlign: 'middle' }}>{session.description || '无'}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default DesktopSessionTable;
