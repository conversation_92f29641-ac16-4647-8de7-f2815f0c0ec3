<template>
  <view class="profile-page">
    <!-- 顶部安全区域 -->
    <view class="status-bar safe-area-inset-top"></view>
    
    <!-- 分享弹窗 -->
    <share-popup ref="sharePopup"></share-popup>

    <!-- 装饰元素 -->
    <view class="decoration-circle decoration-circle-1"></view>
    <view class="decoration-circle decoration-circle-2"></view>
    <view class="decoration-circle decoration-circle-3"></view>

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <view class="avatar-container" :class="{ 'is-member': userInfo.plan_type && userInfo.status === 'active' }">
          <image class="user-avatar" :src="userInfo.avatar || defaultAvatarUrl" mode="aspectFit"></image>
          <view class="avatar-glow"></view>
          <view v-if="userInfo.plan_type && userInfo.status === 'active'" class="vip-badge">
            {{
              userInfo.plan_type === 'monthly' ? '月卡' :
              userInfo.plan_type === 'quarterly' ? '季卡' :
              userInfo.plan_type === 'annual' ? '年卡' : 'VIP'
            }}
          </view>
          <view v-else-if="userInfo.plan_type" class="vip-badge expired">已过期</view>
        </view>
        <view class="user-details">
          <text class="user-name">{{ userInfo.name || '未登录' }}</text>
          <text class="user-id" v-if="userInfo.id">ID: {{ userInfo.id }}</text>
          <!-- 会员信息显示 -->
          <view v-if="userInfo.plan_type && userInfo.status === 'active'" class="member-info">
            <text class="member-expiry">到期: {{ formatDate(userInfo.end_date) }}</text>
            <text class="member-days">剩余: {{ userInfo.remaining_days }}天</text>
          </view>
          <button v-if="!isLoggedIn" class="login-button" @click="goToLogin">
            <text class="login-text">登录/注册</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="navigateTo('/pages/profile/account')">
          <view class="menu-icon-container" style="background-color: rgba(136, 192, 208, 0.1);">
            <image class="menu-icon" src="/static/icons/user.svg" mode="aspectFit" style="filter: invert(70%) sepia(20%) saturate(500%) hue-rotate(165deg) brightness(95%) contrast(95%);"></image>
          </view>
          <text class="menu-label">账号信息</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <!-- 会员中心菜单项，根据isMembershipCenterEnabled控制显示 -->
        <view class="menu-item" v-if="isMembershipCenterEnabled" @click="navigateTo('/pages/member/home')">
          <view class="menu-icon-container" style="background-color: rgba(245, 166, 35, 0.1);">
            <image class="menu-icon" src="/static/icons/crown.svg" mode="aspectFit" style="filter: invert(70%) sepia(50%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%);"></image>
          </view>
          <text class="menu-label">会员中心</text>
          <view v-if="user && user.is_member" class="badge" style="background-color: #f5a623;">VIP</view>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/profile/settings')">
          <view class="menu-icon-container" style="background-color: rgba(163, 190, 140, 0.1);">
            <image class="menu-icon" src="/static/icons/settings.svg" mode="aspectFit" style="filter: invert(80%) sepia(15%) saturate(500%) hue-rotate(60deg) brightness(95%) contrast(90%);"></image>
          </view>
          <text class="menu-label">设置</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/profile/messages')">
          <view class="menu-icon-container" style="background-color: rgba(235, 203, 139, 0.1);">
            <image class="menu-icon" src="/static/icons/message.svg" mode="aspectFit" style="filter: invert(80%) sepia(30%) saturate(500%) hue-rotate(350deg) brightness(100%) contrast(90%);"></image>
          </view>
          <text class="menu-label">我的消息</text>
          <view v-if="unreadMessageCount > 0" class="badge">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/report-template/index')">
          <view class="menu-icon-container" style="background-color: rgba(94, 129, 172, 0.1);">
            <image class="menu-icon" src="/static/icons/document.svg" mode="aspectFit" style="filter: invert(50%) sepia(20%) saturate(700%) hue-rotate(180deg) brightness(90%) contrast(90%);"></image>
          </view>
          <text class="menu-label">报告模板</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/member/faq')">
          <view class="menu-icon-container" style="background-color: rgba(129, 161, 193, 0.1);">
            <image class="menu-icon" src="/static/icons/help-filled.svg" mode="aspectFit" style="filter: invert(60%) sepia(20%) saturate(500%) hue-rotate(180deg) brightness(95%) contrast(90%);"></image>
          </view>
          <text class="menu-label">常见问题</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/profile/feedback')">
          <view class="menu-icon-container" style="background-color: rgba(191, 97, 106, 0.1);">
            <image class="menu-icon" src="/static/icons/feedback.svg" mode="aspectFit" style="filter: invert(50%) sepia(40%) saturate(700%) hue-rotate(320deg) brightness(95%) contrast(95%);"></image>
          </view>
          <text class="menu-label">意见反馈</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="checkUpdate">
          <view class="menu-icon-container" style="background-color: rgba(180, 142, 173, 0.1);">
            <image class="menu-icon" src="/static/icons/update.svg" mode="aspectFit" style="filter: invert(60%) sepia(20%) saturate(500%) hue-rotate(260deg) brightness(90%) contrast(90%);"></image>
          </view>
          <text class="menu-label">检查更新</text>
          <view v-if="hasNewVersion" class="badge">新</view>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click.stop="handleShareClick">
          <view class="menu-icon-container" style="background-color: rgba(100, 181, 246, 0.1);">
            <image class="menu-icon" src="/static/icons/share.svg" mode="aspectFit" style="filter: invert(50%) sepia(90%) saturate(700%) hue-rotate(180deg) brightness(90%) contrast(90%);"></image>
          </view>
          <text class="menu-label">分享有礼</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <view class="menu-item" @click="navigateTo('/pages/profile/about')">
          <view class="menu-icon-container" style="background-color: rgba(129, 161, 193, 0.1);">
            <image class="menu-icon" src="/static/icons/info.svg" mode="aspectFit" style="filter: invert(60%) sepia(20%) saturate(400%) hue-rotate(180deg) brightness(95%) contrast(90%);"></image>
          </view>
          <text class="menu-label">关于我们</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
        <!-- 管理员菜单项 -->
        <view class="menu-item admin-menu-item" v-if="isAdmin" @click="navigateTo('/pages/admin/dashboard')">
          <view class="menu-icon-container admin-icon" style="background-color: rgba(208, 135, 112, 0.15);">
            <image class="menu-icon" src="/static/icons/dashboard.svg" mode="aspectFit" style="filter: invert(60%) sepia(30%) saturate(700%) hue-rotate(340deg) brightness(90%) contrast(90%);"></image>
          </view>
          <text class="menu-label">管理控制台</text>
          <image class="menu-arrow" src="/static/icons/right.svg" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section" v-if="isLoggedIn">
      <button class="logout-button" @click="confirmLogout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text>SoulSync v{{ appVersion }}</text>
    </view>
  </view>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex' // 导入 mapGetters
import appConfig from '../../config/appConfig.js'
import apiService from '../../services/apiService'
import SharePopup from '@/components/SharePopup.vue'
import { formatDate } from '../../utils/dateUtils'

export default {
  components: {
    SharePopup
  },
  data() {
    return {
      userInfo: {
        name: '',
        plan_type: null, // 会员类型: monthly/quarterly/annual
        status: null,    // 会员状态: active/inactive
        avatar: '',
        id: '',
        chatCount: 0,
        moodCount: 0,
        assessmentCount: 0,
        end_date: null,  // 会员截止日期
        remaining_days: 0 // 会员剩余天数
      },
      appVersion: '1.0.0', // 默认版本号，将在onLoad中更新
      hasNewVersion: false,
      latestVersion: null,
      checkingUpdate: false,
      unreadMessageCount: 0, // 未读消息数量
      showSharePopup: false // 控制分享弹窗显示
    }
  },
  computed: {
    ...mapState({
      user: state => state.user.user
    }),
    ...mapGetters('user', ['isMembershipCenterEnabled']), // 引入 isMembershipCenterEnabled getter
    isLoggedIn() {
      return !!this.user
    },
    isAdmin() {
      // 使用 Vuex store 中的 isAdmin getter
      return this.$store.getters['user/isAdmin']
    },
    defaultAvatarUrl() {
      // 根据会员状态返回不同的默认头像路径
      const status = this.userInfo.status;

      if (status === 'active') {
        // 活跃会员使用会员头像
        return '/static/avatars/default-member.svg';
      } else {
        // 非会员或会员已过期使用普通用户头像
        return '/static/avatars/default-user.svg';
      }
    }
  },
  onLoad() {
    console.log('页面加载时的用户数据:', this.user);
    this.loadUserInfo()
    // 获取应用版本号
    this.getAppVersion()
    // 自动检查更新
    this.checkUpdateSilently()

    // 监听消息更新事件
    uni.$on('update-unread-message-count', this.loadUnreadMessageCount)

    // 监听会员状态更新事件
    uni.$on('membershipUpdated', this.handleMembershipUpdated)
  },
    onShow() {
    // 每次页面显示时重新加载用户信息
    this.loadUserInfo()
    // 加载未读消息数量
    this.loadUnreadMessageCount()
    
    // 每次显示页面时都刷新会员状态
    this.$store.dispatch('user/refreshSubscription').then(() => {
      // 刷新后重新加载用户信息以更新显示
      this.loadUserInfo()
    })
  },
  onUnload() {
    // 移除事件监听
    uni.$off('update-unread-message-count', this.loadUnreadMessageCount)
    uni.$off('membershipUpdated', this.handleMembershipUpdated)
  },
  methods: {
    ...mapActions({
      logout: 'user/logout'
    }),

    loadUserInfo() {
      console.log('加载用户信息时的用户数据:', this.user);
      console.log('加载用户信息:', this.user);
      if (this.user) {
        // 如果已登录，使用store中的用户信息
        this.userInfo = {
          name: this.user.name || this.user.nickname || '用户',
          plan_type: this.$store.state.user.subscription?.plan_type || null,
          status: this.$store.state.user.subscription?.status || null,
          avatar: this.user.avatar || '', // 如果没有头像，使用空字符串，会触发默认头像
          id: this.user.id,
          chatCount: this.user.chat_count || 0,
          moodCount: this.user.mood_count || 0,
          assessmentCount: this.user.assessment_count || 0
        }
        console.log('提取后的用户信息:', this.userInfo);
      } else {
        // 如果未登录，使用默认值
        this.userInfo = {
          name: '',
          plan_type: null,
          status: null,
          avatar: '', // 空字符串会触发默认头像
          id: '',
          chatCount: 0,
          moodCount: 0,
          assessmentCount: 0
        }
      }
    },

    navigateTo(url) {
      // 如果需要登录但未登录，跳转到登录页
      if (!this.isLoggedIn && url !== '/pages/profile/about') {
        this.goToLogin()
        return
      }

      // 管理控制台页面需要管理员权限
      if (url === '/pages/admin/dashboard') {
        // 检查用户权限
        const userInfo = this.$store.state.user.user
        console.log('当前用户信息:', userInfo)

        if (!userInfo) {
          console.warn('用户信息未加载，尝试从本地存储恢复')
          // 尝试从本地存储恢复用户信息，只使用标准键名
          const userStr = uni.getStorageSync('user_info')
          if (userStr) {
            try {
              const user = JSON.parse(userStr)
              this.$store.commit('user/SET_USER', user)
              console.log('从本地存储恢复用户信息成功:', user)

              // 重新检查权限
              if (user.role === 'admin' || user.role === 'superadmin') {
                console.log('用户具有管理员权限，跳转到管理控制台')
                uni.navigateTo({
                  url: url
                })
                return
              }
            } catch (e) {
              console.error('解析用户信息失败:', e)
            }
          }
        }

        if (!this.isAdmin) {
          console.warn('用户没有管理员权限')
          uni.showToast({
            title: '您没有权限访问此页面',
            icon: 'none'
          })
          return
        }
      }

      // 直接跳转的页面列表
      const directNavigationPages = [
        '/pages/profile/feedback',
        '/pages/profile/about',
        '/pages/profile/account',
        '/pages/profile/settings',
        '/pages/profile/notification-settings',
        '/pages/profile/messages',
        '/pages/admin/dashboard',
        '/pages/member/home',
        '/pages/member/privileges',
        '/pages/member/plans',
        '/pages/member/usage',
        '/pages/member/faq',
        '/pages/report-template/index'
      ]

      if (directNavigationPages.includes(url)) {
        console.log('直接跳转到页面:', url)
        uni.navigateTo({
          url: url
        })
        return
      }

      // 其他页面暂时显示开发中提示
      console.log('功能开发中:', url)
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },

    confirmLogout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        confirmText: '退出',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.handleLogout()
          }
        }
      })
    },

    async handleLogout() {
      try {
        await this.logout()

        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })

        // 重置用户信息
        this.loadUserInfo()
      } catch (error) {
        console.error('退出登录失败:', error)
        uni.showToast({
          title: '退出登录失败',
          icon: 'none'
        })
      }
    },

    async checkUpdateSilently() {
      try {
        // 使用已获取的应用版本
        const currentVersion = this.appVersion;

        // 调用API检查更新
        const result = await this.$store.dispatch('app/checkUpdate', { silent: true })

        if (result && result.success && result.hasUpdate) {
          this.hasNewVersion = true
          this.latestVersion = result.latestVersion
        }
      } catch (error) {
        console.error('静默检查更新失败:', error)
      }
    },

    // 加载未读消息数量
    async loadUnreadMessageCount() {
      if (!this.isLoggedIn) {
        this.unreadMessageCount = 0
        return
      }

      try {
        // 调用API获取未读消息数量
        const result = await apiService.user.getUnreadMessageCount()

        if (result && result.success) {
          this.unreadMessageCount = result.count || 0
        } else {
          console.error('获取未读消息数量失败:', result && result.error)
          this.unreadMessageCount = 0
        }
      } catch (error) {
        console.error('获取未读消息数量失败:', error)
        this.unreadMessageCount = 0
      }
    },

    // 处理会员状态更新事件
    handleMembershipUpdated(data) {
      console.log('收到会员状态更新事件:', data);
      if (data?.subscription) {
        // 更新Vuex中的订阅状态
        this.$store.commit('user/SET_SUBSCRIPTION', data.subscription);
        
        // 更新页面数据
        this.userInfo = {
          ...this.userInfo,
          plan_type: data.subscription.plan_type,
          status: data.subscription.status
        };
        
        // 显示会员状态更新提示
        if (data.subscription.status === 'active') {
          const memberType = 
            data.subscription.plan_type === 'monthly' ? '月度会员' :
            data.subscription.plan_type === 'quarterly' ? '季度会员' :
            data.subscription.plan_type === 'annual' ? '年度会员' : '会员';
          
          uni.showToast({
            title: `您已成为${memberType}`,
            icon: 'success'
          });
        }
      }
    },

    /**
     * 获取应用版本号
     * 使用多种方法尝试获取真实的应用版本号
     */
    getAppVersion() {
      try {
        console.log('开始获取应用版本号...');

        // 方法1: 从appConfig获取
        const configVersion = appConfig.app.version;
        console.log('从appConfig获取的版本号:', configVersion);

        // 方法2: 直接使用plus.runtime.version
        let plusVersion = null;
        if (typeof plus !== 'undefined' && plus.runtime) {
          plusVersion = plus.runtime.version;
          console.log('从plus.runtime.version获取的版本号:', plusVersion);
        }

        // 方法3: 使用plus.runtime.getProperty
        let propertyVersion = null;
        if (typeof plus !== 'undefined' && plus.runtime && plus.runtime.getProperty) {
          try {
            const appid = plus.runtime.appid;
            if (appid) {
              const widgetInfo = plus.runtime.getProperty(appid);
              if (widgetInfo && widgetInfo.version) {
                propertyVersion = widgetInfo.version;
                console.log('从plus.runtime.getProperty获取的版本号:', propertyVersion);
              }
            }
          } catch (e) {
            console.error('通过plus.runtime.getProperty获取版本号失败:', e);
          }
        }

        // 方法4: 从系统信息获取
        const systemInfo = uni.getSystemInfoSync();
        const systemVersion = systemInfo.appVersion || systemInfo.version;
        console.log('从系统信息获取的版本号:', systemVersion);

        // 按优先级使用版本号
        this.appVersion = plusVersion || propertyVersion || systemVersion || configVersion || '1.0.0';
        console.log('最终使用的版本号:', this.appVersion);
      } catch (e) {
        console.error('获取应用版本号失败:', e);
        // 使用配置中的默认版本
        this.appVersion = appConfig.app.version || '1.0.0';
      }
    },

    async checkUpdate() {
      if (this.checkingUpdate) return

      this.checkingUpdate = true
      uni.showLoading({ title: '检查更新中...' })

      try {
        // 获取当前应用版本
        const currentVersion = this.appVersion;

        // 调用API检查更新
        const result = await this.$store.dispatch('app/checkUpdate', { silent: false })

        uni.hideLoading()

        if (result && result.success) {
          if (result.hasUpdate) {
            this.hasNewVersion = true
            this.latestVersion = result.latestVersion

            // 显示更新提示
            uni.showModal({
              title: '发现新版本',
              content: `当前版本: ${currentVersion}\n最新版本: ${result.latestVersion}\n\n${result.updateInfo || ''}`,
              confirmText: '立即更新',
              cancelText: '稍后再说',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到下载页面或应用商店
                  if (result.downloadUrl) {
                    // 在浏览器中打开下载链接
                    plus.runtime.openURL(result.downloadUrl)
                  }
                }
              }
            })
          } else {
            uni.showToast({
              title: '已是最新版本',
              icon: 'success'
            })
          }
        } else {
          uni.showToast({
            title: result && result.error ? result.error : '检查更新失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        })
        console.error('检查更新失败:', error)
      } finally {
        this.checkingUpdate = false
      }
    },
    
    handleShare() {
      console.log('分享有礼点击事件触发')
      if (!this.isLoggedIn) {
        console.log('用户未登录，跳转登录页')
        uni.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        })
        setTimeout(() => {
          this.goToLogin()
        }, 500)
        return
      }
      
      // 检查用户状态是否有效
      const user = this.$store.state.user.user
      const userId = user?.id || user?.user_id || user?.userId
      if (!user || !userId) {
        console.warn('用户状态异常 - 详细数据:', {
          storeUser: this.$store.state.user,
          localStorage: uni.getStorageSync('user_info'),
          currentUser: user
        })
        uni.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none',
          duration: 2000
        })
        // 尝试刷新用户状态
        this.$store.dispatch('user/refreshUserInfo')
        return
      }
      
      console.log('准备显示分享弹窗')
      this.showSharePopup = true
      this.$nextTick(() => {
        if (this.$refs.sharePopup && this.$refs.sharePopup.open) {
          console.log('调用SharePopup组件open方法')
          this.$refs.sharePopup.open()
        } else {
          console.error('SharePopup组件未正确初始化')
          uni.showToast({
            title: '分享功能暂不可用',
            icon: 'none',
            duration: 2000
          })
        }
      })
    },
    
    closeSharePopup() {
      this.showSharePopup = false
    },
    
    handleShareClick(e) {
      console.log('分享有礼菜单项点击事件', e)
      console.log('当前登录状态:', this.isLoggedIn)
      this.handleShare()
    }
  }
}
</script>

<style>
/* 全局变量 */
page {
  --warm-gradient: linear-gradient(135deg, #f0f0f0 0%, #e5e5e5 100%);
  --soft-gradient: linear-gradient(135deg, #F8F9FC 0%, #f9f1f3 100%);
  --soft-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  --menu-hover: rgba(180, 180, 180, 0.1);
  --menu-active: rgba(180, 180, 180, 0.15);
  --card-radius: 18px;
  --item-radius: 14px;
  --transition-speed: 0.3s;
  --primary-color: #888888;
  --secondary-color: #666666;
  --accent-color: #f5f5f5;
  --text-on-primary: #333333;
}

/* 主页面容器 */
.profile-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 32px;
  position: relative;
  overflow: hidden;
}

/* 装饰圆圈 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
}

.decoration-circle-1 {
  top: -80px;
  right: -80px;
  width: 220px;
  height: 220px;
  background: radial-gradient(circle, rgba(248, 198, 198, 0.15) 0%, rgba(248, 198, 198, 0) 70%);
  animation: float 15s ease-in-out infinite;
}

.decoration-circle-2 {
  bottom: 20%;
  left: -100px;
  width: 280px;
  height: 280px;
  background: radial-gradient(circle, rgba(230, 164, 180, 0.1) 0%, rgba(230, 164, 180, 0) 70%);
  animation: float 18s ease-in-out infinite reverse;
}

.decoration-circle-3 {
  top: 40%;
  right: -60px;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(253, 242, 244, 0.3) 0%, rgba(253, 242, 244, 0) 70%);
  animation: float 12s ease-in-out infinite 2s;
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(-10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* 顶部状态栏 */
.status-bar {
  background: var(--warm-gradient);
  width: 100%;
}

/* 用户卡片 */
.user-card {
  background: #ffffff;
  padding: 15px 10px;
  color: #333;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  border-radius: var(--card-radius);
  overflow: hidden;
  margin: 5px 5px 2px;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  margin-right: 20px;
}

/* 用户头像 */
.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 3px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed);
  position: relative;
  z-index: 2;
  background-color: #fff;
}

.user-avatar:active {
  transform: scale(0.96);
}

/* 普通用户头像边框 */
.avatar-container:not(.is-member) .user-avatar {
  border: 3px solid rgba(240, 240, 240, 0.8);
}

/* 会员用户头像特效 */
.avatar-container.is-member .user-avatar {
  border: 3px solid transparent;
  background-image: linear-gradient(#fff, #fff),
                    linear-gradient(135deg, #FFD700, #FFA500);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

/* 头像光晕效果 */
.avatar-glow {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  z-index: 1;
}

/* 普通用户光晕 */
.avatar-container:not(.is-member) .avatar-glow {
  background: rgba(240, 240, 240, 0.8);
  filter: blur(8px);
  opacity: 0.5;
}

/* 会员用户光晕 */
.avatar-container.is-member .avatar-glow {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.3));
  filter: blur(10px);
  opacity: 0.8;
  animation: glowPulse 2s infinite;
}

@keyframes glowPulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.vip-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: linear-gradient(135deg, #f5a623, #f8c471);
  color: #fff;
  font-size: 11px;
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 2px solid #fff;
  z-index: 3;
  transition: all var(--transition-speed);
}

.vip-badge.expired {
  background: linear-gradient(135deg, #b0bec5, #90a4ae);
  opacity: 0.9;
  font-size: 10px;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

/* 用户详情 */
.user-details {
  flex: 1;
  animation: fadeInUp 0.7s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #333;
  letter-spacing: 0.3px;
  position: relative;
  display: inline-block;
}

.user-id {
  font-size: 13px;
  letter-spacing: 0.3px;
  margin-top: 4px;
  display: inline-block;
  color: #888;
}

/* 登录按钮 */
.login-button {
  margin-top: 12px;
  background-color: #f0f0f0;
  color: #555;
  border: none;
  border-radius: 20px;
  padding: 6px 18px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  transition: left 0.5s;
}

.login-button:active {
  transform: translateY(2px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #e5e5e5;
}

.login-button:active::before {
  left: 100%;
}

.login-text {
  position: relative;
  z-index: 1;
  letter-spacing: 0.3px;
}



/* 菜单部分 */
.menu-section {
  padding: 2px 5px 10px;
  position: relative;
  z-index: 1;
}

.menu-group {
  background-color: white;
  border-radius: var(--card-radius);
  margin-bottom: 20px;
  margin-top: 5px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  transition: all var(--transition-speed);
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.menu-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
}

/* 菜单项 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  transition: all var(--transition-speed);
  position: relative;
}

.menu-item:active {
  background-color: var(--menu-active);
}

.menu-item:last-child {
  border-bottom: none;
}

/* 菜单图标容器 */
.menu-icon-container {
  width: 38px;
  height: 38px;
  border-radius: 10px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  transition: all var(--transition-speed);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.menu-item:active .menu-icon-container {
  transform: scale(0.92);
  background-color: #eeeeee;
}

.admin-icon {
  background-color: #f0f0f0;
}

/* 菜单图标 */
.menu-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

/* 菜单标签 */
.menu-label {
  flex: 1;
  font-size: 15px;
  color: #444;
  font-weight: 500;
  letter-spacing: 0.2px;
}

/* 菜单箭头 */
.menu-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.3;
  transition: transform var(--transition-speed);
  margin-left: 4px;
}

.menu-item:active .menu-arrow {
  transform: translateX(4px);
  opacity: 0.5;
}

/* 徽章 */
.badge {
  background: #f0f0f0;
  color: #666;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  margin-right: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
  font-weight: 500;
  letter-spacing: 0.3px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 退出登录部分 */
.logout-section {
  padding: 0 15px;
  margin-bottom: 30px;
}

.logout-button {
  width: 100%;
  background-color: white;
  color: #e57373;
  border: none;
  border-radius: var(--card-radius);
  padding: 14px 0;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(229, 115, 115, 0.1);
}

.logout-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  background-color: #fff5f5;
}

.logout-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(229, 115, 115, 0.05), transparent);
  transition: left 0.5s;
}

.logout-button:active::before {
  left: 100%;
}

.logout-text {
  position: relative;
  z-index: 1;
  letter-spacing: 0.3px;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 12px;
  color: #aaa;
  padding: 16px;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
  margin-top: 5px;
}

.version-info::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.07), transparent);
}

/* 管理员菜单项特殊样式 */
.admin-menu-item {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 响应式调整 */
@media screen and (min-width: 768px) {
  .menu-section {
    padding: 15px 15% 25px;
  }

  .logout-section {
    padding: 0 15%;
  }
}
</style>