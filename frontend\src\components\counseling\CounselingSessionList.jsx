import React, { useState, useEffect } from 'react';
import { Table, Tag, Button, Space, message, Select, Popconfirm, List, Card, Skeleton, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import counselingService from '../../services/counselingService';
import { PlusOutlined, FileTextOutlined, BarChartOutlined, DeleteOutlined, ClockCircleOutlined, UserOutlined, ReloadOutlined } from '@ant-design/icons';
import moment from 'moment';
import './CounselingSessionList.css';

const { Option } = Select;

/**
 * 心理咨询会话列表组件
 */
const CounselingSessionList = () => {
  const [loading, setLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [role, setRole] = useState('all');
  const navigate = useNavigate();

  // 加载会话列表
  const loadSessions = async () => {
    setLoading(true);
    try {
      console.log('开始加载会话列表');
      const result = await counselingService.getSessions(role);

      if (result.success) {
        console.log('成功加载会话列表:', result.data);

        // 输出每个会话的分析状态，便于调试
        result.data.forEach(session => {
          if (session.analysis) {
            console.log(`会话 ID: ${session.id}, 分析状态: ${session.analysis.status}, 类型: ${typeof session.analysis.status}`);
          } else {
            console.log(`会话 ID: ${session.id}, 无分析数据`);
          }
        });

        // 处理分析状态，确保大小写一致
        const processedSessions = result.data.map(session => {
          if (session.analysis && session.analysis.status) {
            // 将状态标准化为小写
            const lowerStatus = session.analysis.status.toLowerCase();
            console.log(`会话 ID: ${session.id}, 原状态: ${session.analysis.status}, 标准化后: ${lowerStatus}`);

            return {
              ...session,
              analysis: {
                ...session.analysis,
                status: lowerStatus
              }
            };
          }
          return session;
        });

        // 获取每个会话的分析状态
        const sessionsWithAnalysis = await Promise.all(processedSessions.map(async (session) => {
          // 如果会话没有分析数据，尝试获取
          if (!session.analysis) {
            try {
              console.log(`尝试获取会话 ID: ${session.id} 的分析状态`);
              const analysisResult = await counselingService.getSessionAnalysis(session.id);
              if (analysisResult && analysisResult.success) {
                console.log(`成功获取会话 ID: ${session.id} 的分析状态: ${analysisResult.status}`);
                return {
                  ...session,
                  analysis: {
                    ...analysisResult,
                    status: analysisResult.status ? analysisResult.status.toLowerCase() : 'pending' // 确保状态为小写
                  }
                };
              }
            } catch (error) {
              console.error(`获取会话 ID: ${session.id} 的分析状态失败:`, error);
            }
          }
          return session;
        }));

        // 再次检查处理后的状态
        sessionsWithAnalysis.forEach(session => {
          if (session.analysis) {
            console.log(`处理后 - 会话 ID: ${session.id}, 分析状态: ${session.analysis.status}`);
            console.log(`查看报告按钮状态: ${!session.analysis || !session.analysis.status || session.analysis.status.toLowerCase() !== 'completed' ? '禁用' : '启用'}`);
          } else {
            console.log(`处理后 - 会话 ID: ${session.id}, 仍然没有分析数据`);
          }
        });

        setSessions(sessionsWithAnalysis || []);
      } else {
        message.error(result.error || '获取会话列表失败');
      }
    } catch (error) {
      console.error('加载会话列表出错:', error);
      message.error('加载会话列表时发生错误');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadSessions();
  }, [role]);

  // 处理角色切换
  const handleRoleChange = (value) => {
    setRole(value);
  };

  // 处理删除会话
  const handleDeleteSession = async (sessionId) => {
    try {
      const result = await counselingService.deleteSession(sessionId);

      if (result.success) {
        message.success('会话删除成功');
        // 刷新会话列表
        await loadSessions();
      } else {
        message.error(result.error || '删除会话失败');
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      message.error('删除会话失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => navigate(`/counseling/sessions/${record.id}`)}>{text}</a>
      ),
    },
    {
      title: '会话日期',
      dataIndex: 'session_date',
      key: 'session_date',
      render: (text) => new Date(text).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }),
      sorter: (a, b) => moment(a.session_date).unix() - moment(b.session_date).unix(),
    },
    {
      title: '时长',
      dataIndex: 'duration_minutes',
      key: 'duration_minutes',
      render: (text, record) => {
        // 检查是否有录音记录
        const hasRecordings = record.recordings && Array.isArray(record.recordings) && record.recordings.length > 0;

        // 如果有录音记录
        if (hasRecordings) {
          const recording = record.recordings[0];

          // 如果录音有时长信息，显示实际录音时长
          if (recording.duration_seconds && recording.duration_seconds > 0) {
            const totalSeconds = recording.duration_seconds;
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = Math.floor(totalSeconds % 60);
            return `${minutes}:${seconds.toString().padStart(2, '0')} (实际时长)`;
          }

          // 如果有录音但没有时长信息，检查是否有分析报告
          if (record.analysis && record.analysis.status === 'completed') {
            // 有分析报告但没有时长，显示"已分析"
            return text ? `${text} 分钟 (已分析)` : '已分析';
          }

          // 有录音但还没有分析完成，显示配置时长
          return text ? `${text} 分钟 (录音中)` : '录音中';
        }

        // 没有录音记录，显示配置时长或未设置
        return text ? `${text} 分钟` : '未设置';
      },
    },
    {
      title: '姓名',
      dataIndex: 'client_name',
      key: 'client_name',
      render: (text) => text || '未知',
    },
    {
      title: '性别',
      dataIndex: 'client_gender',
      key: 'client_gender',
      render: (text) => {
        return text ? {
          'male': '男',
          'female': '女',
          'other': '其他'
        }[text] || text : '未知';
      },
    },
    {
      title: '年龄',
      dataIndex: 'client_age',
      key: 'client_age',
      render: (text) => text || '未知',
    },
    {
      title: '角色',
      key: 'role',
      render: (_, record) => {
        const isTherapist = record.therapist_id === record.current_user_id;
        const isCreator = record.created_by === record.current_user_id;

        return (
          <Space>
            {isTherapist && <Tooltip title="您在该会话中的角色是咨询师"><Tag color="blue">咨询师</Tag></Tooltip>}
            {isCreator && <Tooltip title="您创建了该会话"><Tag color="green">创建者</Tag></Tooltip>}
            {!isTherapist && !isCreator && <Tooltip title="您在该会话中的角色是来访者/客户"><Tag color="orange">来访者</Tag></Tooltip>}
          </Space>
        );
      },
    },
    {
      title: '分析状态',
      key: 'analysis',
      render: (_, record) => {
        if (!record.analysis) {
          return <Tag color="default">未分析</Tag>;
        }

        // 将状态转换为小写进行比较
        const status = record.analysis.status ? record.analysis.status.toLowerCase() : 'pending';
        console.log(`渲染分析状态 - 会话 ID: ${record.id}, 原状态: ${record.analysis.status}, 标准化后: ${status}`);

        const statusMap = {
          'pending': { color: 'default', text: '待处理' },
          'processing': { color: 'processing', text: '处理中' },
          'completed': { color: 'success', text: '已完成' },
          'failed': { color: 'error', text: '失败' },
        };

        const { color, text } = statusMap[status] || statusMap['pending'];

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '风险等级',
      key: 'riskLevel',
      render: (_, record) => {
        // 如果没有分析数据或分析未完成，返回未知
        if (!record.analysis) {
          return <Tag color="default">未分析</Tag>;
        }

        // 如果分析未完成
        if (!record.analysis.status || record.analysis.status.toLowerCase() !== 'completed') {
          const status = record.analysis.status ? record.analysis.status.toLowerCase() : 'pending';
          const statusText = {
            'pending': '待处理',
            'processing': '分析中',
            'failed': '分析失败'
          }[status] || '未知';

          return <Tag color="default">{statusText}</Tag>;
        }

        // 如果分析完成但没有报告内容
        if (!record.analysis.report_content || (!record.analysis.analysis_results || !record.analysis.analysis_results.mental_state)) {
          return <Tag color="default">未知</Tag>;
        }

        // 尝试从分析结果中获取风险等级
        let riskLevel = '';

        // 从分析结果中获取风险等级
        if (record.analysis.risk_level) {
          // 直接使用分析结果中的风险等级
          riskLevel = record.analysis.risk_level.toLowerCase();
        } else if (record.analysis.analysis_results && record.analysis.analysis_results.mental_state &&
                  record.analysis.analysis_results.mental_state.risk_assessment &&
                  record.analysis.analysis_results.mental_state.risk_assessment.overall_risk) {
          // 从结构化分析中获取风险等级
          riskLevel = record.analysis.analysis_results.mental_state.risk_assessment.overall_risk.toLowerCase();
        } else {
          return <Tag color="default">未知</Tag>;
        }

        // 风险等级标签颜色映射
        const riskLevelColors = {
          'low': 'success',
          'medium': 'warning',
          'high': 'orange',
          'critical': 'error'
        };

        // 风险等级中文名称
        const riskLevelNames = {
          'low': '低风险',
          'medium': '中等风险',
          'high': '高风险',
          'critical': '危急风险'
        };

        // 如果没有找到风险等级，返回未知
        if (!riskLevel || !riskLevelNames[riskLevel]) {
          return <Tag color="default">未知</Tag>;
        }

        return <Tag color={riskLevelColors[riskLevel] || 'default'}>{riskLevelNames[riskLevel]}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          {record.analysis && record.analysis.status && record.analysis.status.toLowerCase() === 'completed' && record.analysis.report_content ? (
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => navigate(`/counseling/sessions/${record.id}/report`)}
            >
              查看报告
            </Button>
          ) : record.recordings && record.recordings.length > 0 ? (
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => navigate(`/counseling/sessions/${record.id}/report`)}
            >
              开始分析
            </Button>
          ) : (
            <Button
              type="text"
              icon={<BarChartOutlined />}
              disabled
            >
              暂无报告
            </Button>
          )}

          <Popconfirm
            title="删除会话"
            description="确定要删除这个会话吗？所有相关的对话、录音和分析数据都将被删除。"
            onConfirm={() => handleDeleteSession(record.id)}
            okText="删除"
            cancelText="取消"
            okButtonProps={{ danger: true }}
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 移除移动端列表渲染
  const renderMobileList = () => {
    return null;
  };

  // 桌面端列表渲染
  const renderDesktopList = () => {
    return (
      <div className="desktop-session-list">
        <Table
          columns={columns}
          dataSource={sessions}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </div>
    );
  };

  // 移除屏幕大小切换逻辑

  return (
    <div className="counseling-session-list">
      <div className="list-header" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Select
            value={role}
            onChange={handleRoleChange}
            style={{ width: 120 }}
          >
            <Option value="all">全部</Option>
            <Option value="therapist">咨询师</Option>
            <Option value="client">客户</Option>
          </Select>
          <Button
            onClick={loadSessions}
            icon={<ReloadOutlined />}
            type="primary"
            size="large"
            style={{ fontWeight: 'bold' }}
          >
            刷新列表
          </Button>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/counseling/sessions/new')}
        >
          新建会话
        </Button>
      </div>

      {/* 只渲染桌面端列表 */}
      {renderDesktopList()}
    </div>
  );
};

export default CounselingSessionList;
